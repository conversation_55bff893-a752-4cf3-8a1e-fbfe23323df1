/**
 * @typedef {object} AccordionProps
 * @property {ReactNode | ReactNode[]} children
 * @property {'light' | 'shadow' | 'bordered' | 'splitted'} [variant='light']
 * @property {'none' | 'single' | 'multiple'} selectionMode
 * @property {'toggle' | 'replace'} [selectionBehavior='toggle']
 * @property {boolean} [isCompact=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [showDivider=true]
 * @property {DividerProps} [dividerProps]
 * @property {boolean} [hideIndicator=false]
 * @property {boolean} [disableAnimation=false]
 * @property {boolean} [disableIndicatorAnimation=false]
 * @property {boolean} [disallowEmptySelection=false]
 * @property {boolean} [keepContentMounted=false]
 * @property {boolean} [fullWidth=true]
 * @property {MotionProps} [motionProps]
 * @property {React.Key[]} [disabledKeys]
 * @property {AccordionItemClassnames} [itemClasses]
 * @property {'all' | React.Key[]} [selectedKeys]
 * @property {'all' | React.Key[]} [defaultSelectedKeys]
 * @property {(keys: "all" | Set<React.Key>) => any} [onSelectionChange]
 */

/**
 * @typedef {object} AccordionItemProps
 * @property {ReactNode} children
 * @property {ReactNode} [title]
 * @property {ReactNode} [subtitle]
 * @property {IndicatorProps} [indicator]
 * @property {ReactNode} [startContent]
 * @property {MotionProps} [motionProps]
 * @property {boolean} [isCompact=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [keepContentMounted=false]
 * @property {boolean} [hideIndicator=false]
 * @property {boolean} [disableAnimation=false]
 * @property {boolean} [disableIndicatorAnimation=false]
 * @property {React.ElementType} [HeadingComponent='h2']
 * @property {AccordionItemClassnames} [classNames]
 * @property {(e: FocusEvent) => void} [onFocus]
 * @property {(e: FocusEvent) => void} [onBlur]
 * @property {(isFocused: boolean) => void} [onFocusChange]
 * @property {(e: KeyboardEvent) => void} [onKeyDown]
 * @property {(e: KeyboardEvent) => void} [onKeyUp]
 * @property {(e: PressEvent) => void} [onPress]
 * @property {(e: PressEvent) => void} [onPressStart]
 * @property {(e: PressEvent) => void} [onPressEnd]
 * @property {(isPressed: boolean) => void} [onPressChange]
 * @property {(e: PressEvent) => void} [onPressUp]
 * @property {MouseEventHandler} [onClick]
 */

/**
 * @typedef {object} IndicatorProps
 * @property {ReactNode | ((props: {isOpen: boolean, isDisabled: boolean, indicator: ReactNode}) => ReactNode)} [children]
 */

/**
 * @typedef {object} AccordionItemClassnames
 * @property {string} [base]
 * @property {string} [heading]
 * @property {string} [trigger]
 * @property {string} [titleWrapper]
 * @property {string} [title]
 * @property {string} [subtitle]
 * @property {string} [startContent]
 * @property {string} [indicator]
 * @property {string} [content]
 */

/**
 * @typedef {object} MotionProps
 * @property {object} [initial]
 * @property {object} [animate]
 * @property {object} [exit]
 * @property {object} [variants]
 * @property {object} [transition]
 */

/**
 * @typedef {object} AlertProps
 * @property {ReactNode} [title]
 * @property {ReactNode} [icon]
 * @property {ReactNode} [description]
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'solid' | 'bordered' | 'flat' | 'faded'} [variant='flat']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius='md']
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {boolean} [isVisible]
 * @property {boolean} [isClosable=false]
 * @property {boolean} [hideIcon=false]
 * @property {boolean} [hideIconWrapper=false]
 * @property {ButtonProps} [closeButtonProps]
 * @property {() => void} [onClose]
 * @property {(isVisible: boolean) => void} [onVisibleChange]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} AutocompleteProps
 * @property {ReactNode[]} children
 * @property {ReactNode} [label]
 * @property {string} [name]
 * @property {'flat' | 'bordered' | 'faded' | 'underlined'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {Iterable<T>} [items]
 * @property {Iterable<T>} [defaultItems]
 * @property {string} [inputValue]
 * @property {string} [defaultInputValue]
 * @property {boolean} [allowsCustomValue=false]
 * @property {boolean} [allowsEmptyCollection=true]
 * @property {boolean} [shouldCloseOnBlur=true]
 * @property {string} [placeholder]
 * @property {ReactNode} [description]
 * @property {'focus' | 'input' | 'manual'} [menuTrigger='focus']
 * @property {'inside' | 'outside' | 'outside-left'} [labelPlacement='inside']
 * @property {React.Key} [selectedKey]
 * @property {React.Key} [defaultSelectedKey]
 * @property {'all' | React.Key[]} [disabledKeys]
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} [errorMessage]
 * @property {(value: { inputValue: string, selectedKey: React.Key }) => ValidationError | true | null | undefined} [validate]
 * @property {'native' | 'aria'} [validationBehavior='native']
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {boolean} [autoFocus=false]
 * @property {(textValue: string, inputValue: string) => boolean} [defaultFilter]
 * @property {Intl.CollatorOptions} [filterOptions]
 * @property {number} [maxListboxHeight=256]
 * @property {number} [itemHeight=32]
 * @property {boolean} [isVirtualized]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isInvalid=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [fullWidth=true]
 * @property {ReactNode} [selectorIcon]
 * @property {ReactNode} [clearIcon]
 * @property {boolean} [showScrollIndicators=true]
 * @property {React.RefObject<HTMLElement>} [scrollRef]
 * @property {InputProps} [inputProps]
 * @property {PopoverProps} [popoverProps]
 * @property {ListboxProps} [listboxProps]
 * @property {ScrollShadowProps} [scrollShadowProps]
 * @property {ButtonProps} [selectorButtonProps]
 * @property {ButtonProps} [clearButtonProps]
 * @property {boolean} [isClearable=true]
 * @property {boolean} [disableClearable=false]
 * @property {boolean} [disableAnimation=true]
 * @property {boolean} [disableSelectorIconRotation=false]
 * @property {(isOpen: boolean, menuTrigger?: MenuTriggerAction) => void} [onOpenChange]
 * @property {(value: string) => void} [onInputChange]
 * @property {(key: React.Key) => void} [onSelectionChange]
 * @property {(e: FocusEvent<HTMLInputElement>) => void} [onFocus]
 * @property {(e: FocusEvent<HTMLInputElement>) => void} [onBlur]
 * @property {(isFocused: boolean) => void} [onFocusChange]
 * @property {(e: KeyboardEvent) => void} [onKeyDown]
 * @property {(e: KeyboardEvent) => void} [onKeyUp]
 * @property {() => void} [onClose]
 * @property {() => void} [onClear]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} AutocompleteItemProps
 * @property {ReactNode} children
 * @property {React.Key} [key]
 * @property {ReactNode} [textValue]
 * @property {ReactNode} [description]
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {ReactNode} [shortcut]
 * @property {boolean} [showDivider=false]
 * @property {boolean} [isReadOnly=false]
 * @property {string} [href]
 * @property {string} [target]
 * @property {object} [className]
 */

/**
 * @typedef {object} AutocompleteSectionProps
 * @property {ReactNode} children
 * @property {ReactNode} [title]
 * @property {boolean} [showDivider=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {'focus' | 'input' | 'manual'} MenuTriggerAction
 */

/**
 * @typedef {object} AvatarProps
 * @property {ReactNode} [children]
 * @property {string} [src]
 * @property {string} [alt]
 * @property {string} [name]
 * @property {ReactNode} [icon]
 * @property {ReactNode} [fallback]
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius='full']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {boolean} [isBordered=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [disableAnimation=false]
 * @property {string} [imgProps]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} AvatarGroupProps
 * @property {ReactNode} children
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius='full']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {boolean} [isBordered=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [disableAnimation=false]
 * @property {number} [max]
 * @property {number} [total]
 * @property {ReactNode} [renderCount]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} BadgeProps
 * @property {ReactNode} children
 * @property {ReactNode} [content]
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'solid' | 'flat' | 'faded' | 'shadow'} [variant='solid']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius='full']
 * @property {'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'} [placement='top-right']
 * @property {'rectangle' | 'circle'} [shape='rectangle']
 * @property {boolean} [showOutline=false]
 * @property {boolean} [isInvisible=false]
 * @property {boolean} [isOneChar=false]
 * @property {boolean} [isDot=false]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} BreadcrumbsProps
 * @property {ReactNode} children
 * @property {'solid' | 'bordered' | 'light'} [variant='solid']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'foreground'} [color='foreground']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {ReactNode} [separator]
 * @property {number} [maxItems]
 * @property {string} [itemsBeforeCollapse=1]
 * @property {string} [itemsAfterCollapse=1]
 * @property {ReactNode} [renderEllipsis]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} ButtonProps
 * @property {ReactNode} [children]
 * @property {'solid' | 'bordered' | 'light' | 'flat' | 'faded' | 'shadow' | 'ghost'} [variant='solid']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {ReactNode} [spinner]
 * @property {'start' | 'end'} [spinnerPlacement='start']
 * @property {boolean} [fullWidth=false]
 * @property {boolean} [isIconOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isLoading=false]
 * @property {boolean} [disableRipple=false]
 * @property {boolean} [disableAnimation=false]
 * @property {(e: PressEvent) => void} [onPress]
 * @property {(e: PressEvent) => void} [onPressStart]
 * @property {(e: PressEvent) => void} [onPressEnd]
 * @property {(isPressed: boolean) => void} [onPressChange]
 * @property {(e: PressEvent) => void} [onPressUp]
 * @property {(e: KeyboardEvent) => void} [onKeyDown]
 * @property {(e: KeyboardEvent) => void} [onKeyUp]
 * @property {MouseEventHandler} [onClick]
 */

/**
 * @typedef {object} ButtonGroupProps
 * @property {ReactNode | ReactNode[]} children
 * @property {'solid' | 'bordered' | 'light' | 'flat' | 'faded' | 'shadow' | 'ghost'} [variant='solid']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius='xl']
 * @property {boolean} [fullWidth=false]
 * @property {boolean} [isDisabled=false]
 */

/**
 * @typedef {object} CalendarProps
 * @property {CalendarDate | null} [value]
 * @property {CalendarDate | null} [defaultValue]
 * @property {CalendarDate} [minValue]
 * @property {CalendarDate} [maxValue]
 * @property {(date: CalendarDate) => boolean} [isDateUnavailable]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [autoFocus=false]
 * @property {boolean} [focusedValue]
 * @property {boolean} [defaultFocusedValue]
 * @property {(value: CalendarDate) => void} [onFocusChange]
 * @property {(value: CalendarDate) => void} [onChange]
 * @property {boolean} [showMonthAndYearPickers=false]
 * @property {boolean} [isHeaderExpanded=false]
 * @property {(isExpanded: boolean) => void} [onHeaderExpandedChange]
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {boolean} [showHelper=true]
 * @property {boolean} [showShadow=false]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} CardProps
 * @property {ReactNode | ReactNode[]} children
 * @property {'none' | 'sm' | 'md' | 'lg'} [shadow='md']
 * @property {'none' | 'sm' | 'md' | 'lg'} [radius='lg']
 * @property {boolean} [fullWidth=false]
 * @property {boolean} [isHoverable=false]
 * @property {boolean} [isPressable=false]
 * @property {boolean} [isBlurred=false]
 * @property {boolean} [isFooterBlurred=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [disableAnimation=false]
 * @property {boolean} [disableRipple=false]
 * @property {boolean} [allowTextSelectionOnPress=false]
 * @property {(e: PressEvent) => void} [onPress]
 * @property {(e: PressEvent) => void} [onPressStart]
 * @property {(e: PressEvent) => void} [onPressEnd]
 * @property {(isPressed: boolean) => void} [onPressChange]
 * @property {(e: PressEvent) => void} [onPressUp]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} CardHeaderProps
 * @property {ReactNode} children
 * @property {object} [className]
 */

/**
 * @typedef {object} CardBodyProps
 * @property {ReactNode} children
 * @property {object} [className]
 */

/**
 * @typedef {object} CardFooterProps
 * @property {ReactNode} children
 * @property {object} [className]
 */

/**
 * @typedef {object} CheckboxProps
 * @property {ReactNode} [children]
 * @property {boolean} [isSelected]
 * @property {boolean} [defaultSelected=false]
 * @property {boolean} [isIndeterminate=false]
 * @property {string} [value]
 * @property {string} [name]
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='primary']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius='md']
 * @property {'solid' | 'bordered' | 'faded'} [lineThrough=false]
 * @property {ReactNode} [icon]
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isInvalid=false]
 * @property {(isSelected: boolean) => void} [onValueChange]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} CheckboxGroupProps
 * @property {ReactNode} children
 * @property {ReactNode} [label]
 * @property {ReactNode} [description]
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} [errorMessage]
 * @property {string[]} [value]
 * @property {string[]} [defaultValue]
 * @property {'vertical' | 'horizontal'} [orientation='vertical']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='primary']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius='md']
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isInvalid=false]
 * @property {(value: string[]) => void} [onValueChange]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} ChipProps
 * @property {ReactNode} children
 * @property {'solid' | 'bordered' | 'light' | 'flat' | 'faded' | 'shadow' | 'dot'} [variant='solid']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius='full']
 * @property {ReactNode} [avatar]
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {boolean} [isClosable=false]
 * @property {boolean} [isDisabled=false]
 * @property {() => void} [onClose]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} CircularProgressProps
 * @property {ReactNode} [children]
 * @property {ReactNode} [label]
 * @property {number} [value]
 * @property {number} [minValue=0]
 * @property {number} [maxValue=100]
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='primary']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {number} [strokeWidth]
 * @property {boolean} [showValueLabel=false]
 * @property {(value: number) => string} [formatOptions]
 * @property {boolean} [isIndeterminate=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} CodeProps
 * @property {ReactNode} children
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius='sm']
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} DateInputProps
 * @property {ReactNode} [label]
 * @property {ReactNode} [placeholder]
 * @property {ReactNode} [description]
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} [errorMessage]
 * @property {CalendarDate | null} [value]
 * @property {CalendarDate | null} [defaultValue]
 * @property {CalendarDate} [minValue]
 * @property {CalendarDate} [maxValue]
 * @property {CalendarDate} [placeholderValue]
 * @property {boolean} [hideTimeZone=false]
 * @property {boolean} [shouldForceLeadingZeros=true]
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isInvalid=false]
 * @property {(value: CalendarDate) => void} [onChange]
 * @property {'flat' | 'bordered' | 'faded' | 'underlined'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {'inside' | 'outside' | 'outside-left'} [labelPlacement='inside']
 * @property {boolean} [fullWidth=true]
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} DatePickerProps
 * @property {ReactNode} [label]
 * @property {ReactNode} [placeholder]
 * @property {ReactNode} [description]
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} [errorMessage]
 * @property {CalendarDate | null} [value]
 * @property {CalendarDate | null} [defaultValue]
 * @property {CalendarDate} [minValue]
 * @property {CalendarDate} [maxValue]
 * @property {CalendarDate} [placeholderValue]
 * @property {boolean} [hideTimeZone=false]
 * @property {boolean} [shouldForceLeadingZeros=true]
 * @property {boolean} [showMonthAndYearPickers=false]
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isInvalid=false]
 * @property {(value: CalendarDate) => void} [onChange]
 * @property {'flat' | 'bordered' | 'faded' | 'underlined'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {'inside' | 'outside' | 'outside-left'} [labelPlacement='inside']
 * @property {boolean} [fullWidth=true]
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {ReactNode} [selectorIcon]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} DividerProps
 * @property {'horizontal' | 'vertical'} [orientation='horizontal']
 * @property {object} [className]
 */

/**
 * @typedef {object} DrawerProps
 * @property {ReactNode} children
 * @property {boolean} [isOpen]
 * @property {boolean} [defaultOpen=false]
 * @property {(isOpen: boolean) => void} [onOpenChange]
 * @property {'top' | 'bottom' | 'left' | 'right'} [placement='right']
 * @property {'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | 'full'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg'} [radius='none']
 * @property {'none' | 'sm' | 'md' | 'lg'} [shadow='sm']
 * @property {ReactNode} [backdrop='opaque']
 * @property {boolean} [isKeyboardDismissDisabled=false]
 * @property {boolean} [isDismissable=true]
 * @property {boolean} [hideCloseButton=false]
 * @property {boolean} [shouldBlockScroll=true]
 * @property {() => void} [onClose]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} DropdownProps
 * @property {ReactNode} children
 * @property {'auto' | 'top' | 'bottom' | 'right' | 'left' | 'top-start' | 'top-end' | 'bottom-start' | 'bottom-end' | 'left-start' | 'left-end' | 'right-start' | 'right-end'} [placement='bottom']
 * @property {'click' | 'hover' | 'focus' | 'longPress'} [trigger='click']
 * @property {'selection' | 'action'} [type='selection']
 * @property {number} [offset=7]
 * @property {number} [delay=0]
 * @property {number} [closeDelay=500]
 * @property {boolean} [isOpen]
 * @property {boolean} [defaultOpen=false]
 * @property {(isOpen: boolean) => void} [onOpenChange]
 * @property {boolean} [shouldBlockScroll=false]
 * @property {boolean} [isKeyboardDismissDisabled=false]
 * @property {boolean} [shouldCloseOnBlur=true]
 * @property {ReactNode} [backdrop='transparent']
 * @property {object} [classNames]
 */

/**
 * @typedef {object} DropdownTriggerProps
 * @property {ReactNode} children
 */

/**
 * @typedef {object} DropdownMenuProps
 * @property {ReactNode} children
 * @property {'static' | 'single' | 'multiple'} [selectionMode='single']
 * @property {React.Key | React.Key[]} [selectedKeys]
 * @property {React.Key | React.Key[]} [defaultSelectedKeys]
 * @property {React.Key[]} [disabledKeys]
 * @property {(keys: React.Key[]) => void} [onSelectionChange]
 * @property {(key: React.Key) => void} [onAction]
 * @property {boolean} [closeOnSelect=true]
 * @property {boolean} [disallowEmptySelection=false]
 * @property {'none' | 'sm' | 'md' | 'lg'} [variant='solid']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} DropdownItemProps
 * @property {ReactNode} children
 * @property {React.Key} [key]
 * @property {ReactNode} [textValue]
 * @property {ReactNode} [description]
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {ReactNode} [shortcut]
 * @property {boolean} [showDivider=false]
 * @property {boolean} [isReadOnly=false]
 * @property {string} [href]
 * @property {string} [target]
 * @property {object} [className]
 */

/**
 * @typedef {object} DropdownSectionProps
 * @property {ReactNode} children
 * @property {ReactNode} [title]
 * @property {boolean} [showDivider=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} FormProps
 * @property {ReactNode} children
 * @property {object} [validationErrors]
 * @property {boolean} [validationBehavior='native']
 * @property {(e: FormEvent) => void} [onSubmit]
 * @property {() => void} [onReset]
 * @property {boolean} [onInvalid]
 * @property {object} [className]
 */

/**
 * @typedef {object} ImageProps
 * @property {string} src
 * @property {string} [alt]
 * @property {number | string} [width]
 * @property {number | string} [height]
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius='none']
 * @property {'none' | 'sm' | 'md' | 'lg'} [shadow='none']
 * @property {boolean} [isBlurred=false]
 * @property {boolean} [isZoomed=false]
 * @property {ReactNode} [fallbackSrc]
 * @property {boolean} [loading='lazy']
 * @property {boolean} [disableSkeleton=false]
 * @property {() => void} [onLoad]
 * @property {() => void} [onError]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} InputProps
 * @property {ReactNode} [children]
 * @property {'flat' | 'bordered' | 'faded' | 'underlined'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {ReactNode} [label]
 * @property {string} [value]
 * @property {string} [defaultValue]
 * @property {string} [placeholder]
 * @property {ReactNode} [description]
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} [errorMessage]
 * @property {(value: string) => ValidationError | true | null | undefined} [validate]
 * @property {'native' | 'aria'} [validationBehavior='native']
 * @property {number} [minLength]
 * @property {number} [maxLength]
 * @property {string} [pattern]
 * @property {'text' | 'email' | 'url' | 'password' | 'tel' | 'search' | 'file'} [type='text']
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {'inside' | 'outside' | 'outside-left'} [labelPlacement='inside']
 * @property {boolean} [fullWidth=true]
 * @property {boolean} [isClearable=false]
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isInvalid=false]
 * @property {RefObject<HTMLDivElement>} [baseRef]
 * @property {boolean} [disableAnimation=false]
 * @property {(e: React.ChangeEvent<HTMLInputElement>) => void} [onChange]
 * @property {(value: string) => void} [onValueChange]
 * @property {() => void} [onClear]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} InputOTPProps
 * @property {number} [length=6]
 * @property {string} [value]
 * @property {string} [defaultValue]
 * @property {ReactNode} [description]
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} [errorMessage]
 * @property {'flat' | 'bordered' | 'faded' | 'underlined'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isInvalid=false]
 * @property {(value: string) => void} [onValueChange]
 * @property {() => void} [onComplete]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} KbdProps
 * @property {ReactNode} children
 * @property {string[]} [keys]
 * @property {ReactNode} [abbr]
 * @property {ReactNode} [title]
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {object} [classNames]
 */

/**
 * @typedef {object} LinkProps
 * @property {ReactNode} children
 * @property {string} [href]
 * @property {string} [target]
 * @property {'foreground' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='primary']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {boolean} [underline='none']
 * @property {boolean} [isBlock=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isExternal=false]
 * @property {boolean} [showAnchorIcon=false]
 * @property {ReactNode} [anchorIcon]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [className]
 */

/**
 * @typedef {object} ListboxProps
 * @property {ReactNode} children
 * @property {ReactNode} [topContent]
 * @property {ReactNode} [bottomContent]
 * @property {ReactNode} [emptyContent]
 * @property {'none' | 'single' | 'multiple'} [selectionMode='none']
 * @property {React.Key | React.Key[]} [selectedKeys]
 * @property {React.Key | React.Key[]} [defaultSelectedKeys]
 * @property {React.Key[]} [disabledKeys]
 * @property {(keys: React.Key[]) => void} [onSelectionChange]
 * @property {(key: React.Key) => void} [onAction]
 * @property {'solid' | 'bordered' | 'light' | 'flat' | 'faded' | 'shadow'} [variant='solid']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {boolean} [autoFocus=false]
 * @property {boolean} [shouldFocusWrap=false]
 * @property {boolean} [disallowEmptySelection=false]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} ListboxItemProps
 * @property {ReactNode} children
 * @property {React.Key} [key]
 * @property {ReactNode} [textValue]
 * @property {ReactNode} [description]
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {ReactNode} [shortcut]
 * @property {boolean} [showDivider=false]
 * @property {boolean} [isReadOnly=false]
 * @property {string} [href]
 * @property {string} [target]
 * @property {object} [className]
 */

/**
 * @typedef {object} ListboxSectionProps
 * @property {ReactNode} children
 * @property {ReactNode} [title]
 * @property {boolean} [showDivider=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} MenuProps
 * @property {ReactNode} children
 * @property {ReactNode} [topContent]
 * @property {ReactNode} [bottomContent]
 * @property {ReactNode} [emptyContent]
 * @property {'none' | 'single' | 'multiple'} [selectionMode='none']
 * @property {React.Key | React.Key[]} [selectedKeys]
 * @property {React.Key | React.Key[]} [defaultSelectedKeys]
 * @property {React.Key[]} [disabledKeys]
 * @property {(keys: React.Key[]) => void} [onSelectionChange]
 * @property {(key: React.Key) => void} [onAction]
 * @property {'solid' | 'bordered' | 'light' | 'flat' | 'faded' | 'shadow'} [variant='solid']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {boolean} [closeOnSelect=true]
 * @property {boolean} [autoFocus=false]
 * @property {boolean} [shouldFocusWrap=false]
 * @property {boolean} [disallowEmptySelection=false]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} MenuItemProps
 * @property {ReactNode} children
 * @property {React.Key} [key]
 * @property {ReactNode} [textValue]
 * @property {ReactNode} [description]
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {ReactNode} [shortcut]
 * @property {boolean} [showDivider=false]
 * @property {boolean} [isReadOnly=false]
 * @property {string} [href]
 * @property {string} [target]
 * @property {object} [className]
 */

/**
 * @typedef {object} MenuSectionProps
 * @property {ReactNode} children
 * @property {ReactNode} [title]
 * @property {boolean} [showDivider=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} ModalProps
 * @property {ReactNode} children
 * @property {boolean} [isOpen]
 * @property {boolean} [defaultOpen=false]
 * @property {(isOpen: boolean) => void} [onOpenChange]
 * @property {'auto' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | 'full'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg'} [radius='lg']
 * @property {'none' | 'sm' | 'md' | 'lg'} [shadow='lg']
 * @property {'center' | 'top' | 'top-center' | 'bottom' | 'bottom-center'} [placement='center']
 * @property {'opaque' | 'transparent' | 'blur'} [backdrop='opaque']
 * @property {number} [scrollBehavior='inside']
 * @property {boolean} [isKeyboardDismissDisabled=false]
 * @property {boolean} [isDismissable=true]
 * @property {boolean} [hideCloseButton=false]
 * @property {boolean} [shouldBlockScroll=true]
 * @property {() => void} [onClose]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} ModalContentProps
 * @property {ReactNode} children
 * @property {object} [className]
 */

/**
 * @typedef {object} ModalHeaderProps
 * @property {ReactNode} children
 * @property {object} [className]
 */

/**
 * @typedef {object} ModalBodyProps
 * @property {ReactNode} children
 * @property {object} [className]
 */

/**
 * @typedef {object} ModalFooterProps
 * @property {ReactNode} children
 * @property {object} [className]
 */

/**
 * @typedef {object} NavbarProps
 * @property {ReactNode} children
 * @property {'static' | 'sticky' | 'floating'} [position='sticky']
 * @property {number | string} [maxWidth='lg']
 * @property {boolean} [isBordered=false]
 * @property {boolean} [isBlurred=true]
 * @property {boolean} [disableAnimation=false]
 * @property {boolean} [disableScrollHandler=false]
 * @property {boolean} [shouldHideOnScroll=false]
 * @property {number} [height=64]
 * @property {() => void} [onScrollPositionChange]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} NavbarBrandProps
 * @property {ReactNode} children
 * @property {object} [className]
 */

/**
 * @typedef {object} NavbarContentProps
 * @property {ReactNode} children
 * @property {'start' | 'center' | 'end'} [justify='start']
 * @property {object} [className]
 */

/**
 * @typedef {object} NavbarItemProps
 * @property {ReactNode} children
 * @property {boolean} [isActive=false]
 * @property {object} [className]
 */

/**
 * @typedef {object} NavbarMenuProps
 * @property {ReactNode} children
 * @property {object} [className]
 */

/**
 * @typedef {object} NavbarMenuItemProps
 * @property {ReactNode} children
 * @property {object} [className]
 */

/**
 * @typedef {object} NavbarMenuToggleProps
 * @property {ReactNode} [children]
 * @property {ReactNode} [icon]
 * @property {boolean} [srOnly=false]
 * @property {object} [className]
 */

/**
 * @typedef {object} NumberInputProps
 * @property {ReactNode} [label]
 * @property {ReactNode} [placeholder]
 * @property {ReactNode} [description]
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} [errorMessage]
 * @property {number} [value]
 * @property {number} [defaultValue]
 * @property {number} [minValue]
 * @property {number} [maxValue]
 * @property {number} [step=1]
 * @property {'flat' | 'bordered' | 'faded' | 'underlined'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {'inside' | 'outside' | 'outside-left'} [labelPlacement='inside']
 * @property {boolean} [fullWidth=true]
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isInvalid=false]
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {boolean} [disableAnimation=false]
 * @property {(value: number) => void} [onValueChange]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} PaginationProps
 * @property {number} [total=1]
 * @property {number} [page]
 * @property {number} [defaultPage=1]
 * @property {number} [initialPage=1]
 * @property {number} [siblings=1]
 * @property {number} [boundaries=1]
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='primary']
 * @property {'flat' | 'bordered' | 'light' | 'faded'} [variant='flat']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {boolean} [isCompact=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [showControls=false]
 * @property {boolean} [showShadow=false]
 * @property {boolean} [disableCursorAnimation=false]
 * @property {boolean} [disableAnimation=false]
 * @property {(page: number) => void} [onChange]
 * @property {ReactNode} [renderItem]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} PopoverProps
 * @property {ReactNode} children
 * @property {'auto' | 'top' | 'bottom' | 'right' | 'left' | 'top-start' | 'top-end' | 'bottom-start' | 'bottom-end' | 'left-start' | 'left-end' | 'right-start' | 'right-end'} [placement='top']
 * @property {'click' | 'hover' | 'focus' | 'longPress'} [trigger='click']
 * @property {number} [offset=7]
 * @property {number} [delay=0]
 * @property {number} [closeDelay=500]
 * @property {boolean} [isOpen]
 * @property {boolean} [defaultOpen=false]
 * @property {(isOpen: boolean) => void} [onOpenChange]
 * @property {boolean} [shouldBlockScroll=false]
 * @property {boolean} [isKeyboardDismissDisabled=false]
 * @property {boolean} [shouldCloseOnBlur=true]
 * @property {boolean} [shouldFlip=true]
 * @property {boolean} [shouldUpdatePosition=true]
 * @property {'opaque' | 'transparent' | 'blur'} [backdrop='transparent']
 * @property {boolean} [showArrow=false]
 * @property {number} [containerPadding=12]
 * @property {number} [crossOffset=0]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} PopoverTriggerProps
 * @property {ReactNode} children
 */

/**
 * @typedef {object} PopoverContentProps
 * @property {ReactNode} children
 * @property {object} [className]
 */

/**
 * @typedef {object} ProgressProps
 * @property {ReactNode} [label]
 * @property {number} [value]
 * @property {number} [minValue=0]
 * @property {number} [maxValue=100]
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='primary']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius='full']
 * @property {number} [strokeWidth]
 * @property {boolean} [showValueLabel=false]
 * @property {(value: number) => string} [formatOptions]
 * @property {boolean} [isIndeterminate=false]
 * @property {boolean} [isStriped=false]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} RadioGroupProps
 * @property {ReactNode} children
 * @property {ReactNode} [label]
 * @property {ReactNode} [description]
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} [errorMessage]
 * @property {string} [value]
 * @property {string} [defaultValue]
 * @property {string} [name]
 * @property {'vertical' | 'horizontal'} [orientation='vertical']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='primary']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isInvalid=false]
 * @property {(value: string) => void} [onValueChange]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} RadioProps
 * @property {ReactNode} [children]
 * @property {string} value
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='primary']
 * @property {ReactNode} [description]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} ScrollShadowProps
 * @property {ReactNode} children
 * @property {'vertical' | 'horizontal' | 'both'} [orientation='vertical']
 * @property {boolean} [hideScrollBar=false]
 * @property {number} [offset=0]
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {boolean} [isEnabled=true]
 * @property {() => void} [onVisibilityChange]
 * @property {object} [className]
 */

/**
 * @typedef {object} SelectProps
 * @property {ReactNode} children
 * @property {ReactNode} [label]
 * @property {ReactNode} [placeholder]
 * @property {ReactNode} [description]
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} [errorMessage]
 * @property {'none' | 'single' | 'multiple'} [selectionMode='single']
 * @property {React.Key | React.Key[]} [selectedKeys]
 * @property {React.Key | React.Key[]} [defaultSelectedKeys]
 * @property {React.Key[]} [disabledKeys]
 * @property {(keys: React.Key[]) => void} [onSelectionChange]
 * @property {'flat' | 'bordered' | 'faded' | 'underlined'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {'inside' | 'outside' | 'outside-left'} [labelPlacement='inside']
 * @property {boolean} [fullWidth=true]
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isInvalid=false]
 * @property {boolean} [isMultiline=false]
 * @property {boolean} [disallowEmptySelection=false]
 * @property {boolean} [disableAnimation=false]
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {ReactNode} [selectorIcon]
 * @property {ReactNode} [popoverProps]
 * @property {ReactNode} [listboxProps]
 * @property {ReactNode} [scrollShadowProps]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} SelectItemProps
 * @property {ReactNode} children
 * @property {React.Key} [key]
 * @property {ReactNode} [textValue]
 * @property {ReactNode} [description]
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {object} [className]
 */

/**
 * @typedef {object} SelectSectionProps
 * @property {ReactNode} children
 * @property {ReactNode} [title]
 * @property {boolean} [showDivider=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} SkeletonProps
 * @property {ReactNode} [children]
 * @property {boolean} [isLoaded=false]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [className]
 */

/**
 * @typedef {object} SliderProps
 * @property {ReactNode} [label]
 * @property {number | number[]} [value]
 * @property {number | number[]} [defaultValue]
 * @property {number} [minValue=0]
 * @property {number} [maxValue=100]
 * @property {number} [step=1]
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='primary']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius='full']
 * @property {'vertical' | 'horizontal'} [orientation='horizontal']
 * @property {boolean} [showSteps=false]
 * @property {boolean} [showTooltip=false]
 * @property {boolean} [showOutline=false]
 * @property {boolean} [hideValue=false]
 * @property {boolean} [hideThumb=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [disableThumbScale=false]
 * @property {boolean} [disableAnimation=false]
 * @property {(value: number | number[]) => void} [onChange]
 * @property {(value: number | number[]) => void} [onChangeEnd]
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {ReactNode} [renderThumb]
 * @property {ReactNode} [renderLabel]
 * @property {ReactNode} [renderValue]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} SnippetProps
 * @property {ReactNode} children
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'flat' | 'solid' | 'bordered' | 'shadow'} [variant='flat']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius='lg']
 * @property {ReactNode} [symbol='$']
 * @property {number} [timeout=2000]
 * @property {boolean} [hideCopyButton=false]
 * @property {boolean} [hideSymbol=false]
 * @property {boolean} [disableAnimation=false]
 * @property {boolean} [disableTooltip=false]
 * @property {string} [copyIcon]
 * @property {string} [checkIcon]
 * @property {string} [tooltipProps]
 * @property {() => void} [onCopy]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} SpacerProps
 * @property {number} [x=1]
 * @property {number} [y=1]
 * @property {object} [className]
 */

/**
 * @typedef {object} SpinnerProps
 * @property {ReactNode} [label]
 * @property {ReactNode} [labelColor]
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'current'} [color='primary']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {object} [classNames]
 */

/**
 * @typedef {object} SwitchProps
 * @property {ReactNode} [children]
 * @property {boolean} [isSelected]
 * @property {boolean} [defaultSelected=false]
 * @property {string} [value]
 * @property {string} [name]
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='primary']
 * @property {ReactNode} [thumbIcon]
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {(isSelected: boolean) => void} [onValueChange]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} TableProps
 * @property {ReactNode} children
 * @property {ReactNode} [topContent]
 * @property {ReactNode} [topContentPlacement='inside']
 * @property {ReactNode} [bottomContent]
 * @property {ReactNode} [bottomContentPlacement='inside']
 * @property {ReactNode} [emptyContent]
 * @property {'none' | 'single' | 'multiple'} [selectionMode='none']
 * @property {'all' | React.Key[]} [selectedKeys]
 * @property {'all' | React.Key[]} [defaultSelectedKeys]
 * @property {React.Key[]} [disabledKeys]
 * @property {(keys: React.Key[]) => void} [onSelectionChange]
 * @property {(key: React.Key) => void} [onRowAction]
 * @property {'ascending' | 'descending'} [sortDescriptor]
 * @property {(descriptor: SortDescriptor) => void} [onSortChange]
 * @property {'default' | 'striped'} [color='default']
 * @property {boolean} [isHeaderSticky=false]
 * @property {boolean} [isCompact=false]
 * @property {boolean} [removeWrapper=false]
 * @property {boolean} [disallowEmptySelection=false]
 * @property {boolean} [allowsSorting=false]
 * @property {boolean} [disableAnimation=false]
 * @property {string} [baseRef]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} TableHeaderProps
 * @property {ReactNode} children
 * @property {object} [columns]
 * @property {object} [className]
 */

/**
 * @typedef {object} TableColumnProps
 * @property {ReactNode} children
 * @property {React.Key} [key]
 * @property {boolean} [allowsSorting=false]
 * @property {number} [width]
 * @property {number} [minWidth]
 * @property {number} [maxWidth]
 * @property {'start' | 'center' | 'end'} [align='start']
 * @property {boolean} [hideHeader=false]
 * @property {object} [className]
 */

/**
 * @typedef {object} TableBodyProps
 * @property {ReactNode} children
 * @property {ReactNode} [emptyContent]
 * @property {boolean} [isLoading=false]
 * @property {ReactNode} [loadingContent]
 * @property {object} [items]
 * @property {object} [className]
 */

/**
 * @typedef {object} TableRowProps
 * @property {ReactNode} children
 * @property {React.Key} [key]
 * @property {string} [href]
 * @property {object} [className]
 */

/**
 * @typedef {object} TableCellProps
 * @property {ReactNode} children
 * @property {object} [className]
 */

/**
 * @typedef {object} TabsProps
 * @property {ReactNode} children
 * @property {'horizontal' | 'vertical'} [orientation='horizontal']
 * @property {'start' | 'center' | 'end'} [placement='top']
 * @property {'solid' | 'bordered' | 'light' | 'underlined'} [variant='solid']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {boolean} [fullWidth=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [disableAnimation=false]
 * @property {boolean} [disableCursorAnimation=false]
 * @property {boolean} [shouldSelectOnPressUp=false]
 * @property {React.Key} [selectedKey]
 * @property {React.Key} [defaultSelectedKey]
 * @property {React.Key[]} [disabledKeys]
 * @property {(key: React.Key) => void} [onSelectionChange]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} TabProps
 * @property {ReactNode} children
 * @property {React.Key} [key]
 * @property {ReactNode} [title]
 * @property {ReactNode} [titleValue]
 * @property {boolean} [isDisabled=false]
 * @property {string} [href]
 * @property {string} [target]
 * @property {object} [className]
 */

/**
 * @typedef {object} TextareaProps
 * @property {ReactNode} [label]
 * @property {string} [value]
 * @property {string} [defaultValue]
 * @property {string} [placeholder]
 * @property {ReactNode} [description]
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} [errorMessage]
 * @property {(value: string) => ValidationError | true | null | undefined} [validate]
 * @property {'native' | 'aria'} [validationBehavior='native']
 * @property {number} [minLength]
 * @property {number} [maxLength]
 * @property {string} [pattern]
 * @property {number} [rows]
 * @property {number} [minRows=3]
 * @property {number} [maxRows=8]
 * @property {'flat' | 'bordered' | 'faded' | 'underlined'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {'inside' | 'outside' | 'outside-left'} [labelPlacement='inside']
 * @property {boolean} [fullWidth=true]
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isInvalid=false]
 * @property {boolean} [disableAnimation=false]
 * @property {boolean} [disableAutosize=false]
 * @property {(e: React.ChangeEvent<HTMLTextAreaElement>) => void} [onChange]
 * @property {(value: string) => void} [onValueChange]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} TimeInputProps
 * @property {ReactNode} [label]
 * @property {ReactNode} [placeholder]
 * @property {ReactNode} [description]
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} [errorMessage]
 * @property {Time | null} [value]
 * @property {Time | null} [defaultValue]
 * @property {Time} [minValue]
 * @property {Time} [maxValue]
 * @property {Time} [placeholderValue]
 * @property {number} [hourCycle=24]
 * @property {boolean} [hideTimeZone=false]
 * @property {boolean} [shouldForceLeadingZeros=true]
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isInvalid=false]
 * @property {(value: Time) => void} [onChange]
 * @property {'flat' | 'bordered' | 'faded' | 'underlined'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {'inside' | 'outside' | 'outside-left'} [labelPlacement='inside']
 * @property {boolean} [fullWidth=true]
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {boolean} [disableAnimation=false]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} ToastProps
 * @property {ReactNode} children
 * @property {ReactNode} [title]
 * @property {ReactNode} [description]
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'solid' | 'bordered' | 'flat' | 'faded' | 'shadow'} [variant='solid']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius='lg']
 * @property {ReactNode} [icon]
 * @property {ReactNode} [action]
 * @property {boolean} [isClosable=true]
 * @property {number} [duration=5000]
 * @property {() => void} [onClose]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} TooltipProps
 * @property {ReactNode} children
 * @property {ReactNode} content
 * @property {'auto' | 'top' | 'bottom' | 'right' | 'left' | 'top-start' | 'top-end' | 'bottom-start' | 'bottom-end' | 'left-start' | 'left-end' | 'right-start' | 'right-end'} [placement='top']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'foreground' | 'invert'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius='md']
 * @property {number} [delay=0]
 * @property {number} [closeDelay=500]
 * @property {number} [offset=7]
 * @property {boolean} [isOpen]
 * @property {boolean} [defaultOpen=false]
 * @property {(isOpen: boolean) => void} [onOpenChange]
 * @property {boolean} [showArrow=false]
 * @property {boolean} [isKeyboardDismissDisabled=false]
 * @property {boolean} [shouldCloseOnBlur=true]
 * @property {boolean} [shouldFlip=true]
 * @property {boolean} [shouldUpdatePosition=true]
 * @property {boolean} [disableAnimation=false]
 * @property {ReactNode} [motionProps]
 * @property {number} [containerPadding=12]
 * @property {number} [crossOffset=0]
 * @property {object} [classNames]
 */

/**
 * @typedef {object} UserProps
 * @property {ReactNode} [name]
 * @property {ReactNode} [description]
 * @property {string} [avatarSrc]
 * @property {ReactNode} [avatarIcon]
 * @property {ReactNode} [avatarFallback]
 * @property {object} [avatarProps]
 * @property {object} [classNames]
 */
